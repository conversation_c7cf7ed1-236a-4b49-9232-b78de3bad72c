import { Msg } from "../../../proto/msg-define"
import { EquipMakeCfg, SpaceStoneCfg, TimeStoneCfg } from "../../common/constant/DataType"
import { ConditionType, ItemID } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import { BattleTeam } from "../battle/BattleMgr"
import BattleRole from "../battle/BattleRole"
import { BlackHoleNode } from "../blackHole/BlackHoleModel"
import ConditionObj from "../common/ConditionObj"
import { Equip } from "../equip/EquipModel"
@mc.addmodel('timeStone')
export default class TimeStoneModel extends mc.BaseModel {
    private lv: number = 0
    private energy: number = 0

    public data: proto.ITimeStone
    private firstSync: boolean = false
    public records: EventBase[] = []

    public getLv() { return this.lv }
    public getEnergy() { return this.energy }
    public getMaxEnergy() { return this.getJson()?.energy }
    public getMaxEvent() { return this.getJson()?.eventMax }

    public init() {
        gameHelper.net.on(Msg.S2C_OnTimeStoneRecordMessage, this.onTimeStoneRecord, this)
        this.updateInfo(this.data)
        this.data = null
        this.listenEvents()
    }

    private listenEvents() {
        eventCenter.on(EventType.TIMESTONE_USE, this.onUse, this)
    }

    public updateInfo(data: proto.ITimeStone) {
        this.lv = this.data.lv || 0
        this.energy = this.data.energy || 0
    }

    public async lvUp() {
        const cost = gameHelper.toConditions(this.getLvUpCost())
        if (!gameHelper.checkConditions(cost)) {
            return viewHelper.showAlert('title_jumpTips_3')
        }
        let { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_TimeStoneLvUpMessage)
        if (code != 0) {
            return void viewHelper.showNetError(code)
        }
        gameHelper.deductConditions(cost)
        if (this.lv == 0) {
            gameHelper.grantReward(new ConditionObj().init(ConditionType.PROP, ItemID.TIMESTONE, 1))
        }
        let preMaxEnergy = this.getJson()?.energy || 0
        this.lv++
        if (this.lv == 1) {
            this.energy = this.getJson().energy
        }
        else {
            this.energy += this.getJson().energy - preMaxEnergy
        }
        eventCenter.emit(EventType.TIMESTONE_LV_UP)
        return true
    }

    public getJson() {
        return assetsMgr.getJsonData<TimeStoneCfg>("TimeStone", this.lv)
    }

    public getLvUpCost() {
        return assetsMgr.getJsonData<TimeStoneCfg>("TimeStone", this.lv + 1)?.buyCost
    }

    public getDailyRecover() {
        return this.getJson()?.dailyRecover || 0
    }

    public async showAnimation(type: proto.TimeStoneEvent) {
        const pnlKey = "timeStone/TimeStoneActionPnl"
        await new Promise(resolve => viewHelper.showPnl(pnlKey, { type, onAnimationEnd: resolve }))
    }

    public async syncData() {
        if (this.firstSync) return
        this.firstSync = true
        const r = await gameHelper.net.requestWithData(Msg.C2S_TimeStoneRecordMessage)
        this.records.length = 0
        this.onTimeStoneRecord(r)
    }

    private onTimeStoneRecord(r: proto.S2C_OnTimeStoneRecordMessage) {
        const list = r.records
        if (!list) return
        const ary = []
        for (const { type, data } of list) {
            const event = EventMap[type]
            const evtProto = EventProtoMap[type]
            const evt = EventBase.decode(evtProto.decode(data.value), event)
            ary.push(evt)
        }
        this.records.unshift(...ary)
        const diff = this.records.length - this.getMaxEvent()
        if (diff > 0) {
            this.records.splice(this.records.length - diff, diff)
        }
    }

    @ut.addLock
    private async onUse(arg: { evts: number[], type: proto.TimeStoneEvent, ids: number[], equip: Equip }) {
        const { evts, type, ids, equip } = arg
        if (!evts || !evts.length) return false
        let cost = 0
        const index: number[] = []
        for (const evt of evts) {
            const data = this.records[evt]
            if (data.type != type) return false
            cost += data.cost
            index.push(evt)
        }
        if (cost > this.energy) {
            viewHelper.showAlert("timeStone_play_tips_1")
            return false
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_TimeStoneUseMessage, { type, index })
        if (r.code != 0) {
            viewHelper.showNetError(r.code)
            return false
        }
        await this.showAnimation(type)
        this.energy -= cost
        const addAry: ConditionObj[] = []
        const deductAry: ConditionObj[] = []
        const deductFnAry: Function[] = []
        for (const evt of evts) {
            const data = this.records[evt]
            data.expired = true
            const { add, deduct, deductFn } = data.logic(r.extra)
            add && add.forEach(item => addAry.push(item))
            deduct && deduct.forEach(item => deductAry.push(item))
            deductFn && deductFn.forEach(item => deductFnAry.push(item))
        }
        gameHelper.deductConditions(deductAry)
        deductFnAry.forEach(item => item())
        gameHelper.grantRewards(addAry)
        switch (type) {
            case proto.TimeStoneEvent.TypeJackpot:
                eventCenter.emit(EventType.TIMESTONE_USE_JACKPOT, evts, addAry, ids)
                break
            case proto.TimeStoneEvent.TypeEquipMake:
                eventCenter.emit(EventType.TIMESTONE_USE_EQUIP_MAKE, addAry, equip)
                break
            case proto.TimeStoneEvent.TypeBlackHoleBattle:
                eventCenter.emit(EventType.TIMESTONE_USE_BLACK_HOLE_BATTLE)
                break
        }
        return true
    }
}

export class EventBase {
    public type: proto.TimeStoneEvent = proto.TimeStoneEvent.TypeNone
    public expired: boolean = false

    get cost() {
        const data = assetsMgr.getJsonData<any>("TimeStoneEvent", this.type)
        return data ? data.cost : 0
    }

    public static decode(data: any, cls: new () => EventBase) {
        const event = new cls()
        event.from(data)
        return event
    }

    public from(data: any) { throw new Error("Method not implemented."); }

    public logic(extra: google.protobuf.IAny): { add: ConditionObj[], deduct: ConditionObj[], deductFn: Function[] } { throw new Error("Method not implemented."); }

}

export class JackpotEvent extends EventBase {
    public type: proto.TimeStoneEvent = proto.TimeStoneEvent.TypeJackpot
    public drawType: number
    public drawPid: number
    public isConvertIntoFragments: boolean
    public isDiamondDiscount: boolean

    public from(data: proto.IJackpotEventData) {
        this.drawType = data.drawType
        this.drawPid = data.drawPid
        this.isConvertIntoFragments = data.isConvertIntoFragments
        this.isDiamondDiscount = data.isDiamondDiscount
        this.expired = data.expired
    }

    public logic() {
        const add: ConditionObj[] = []
        const deduct: ConditionObj[] = []
        const deductFn: Function[] = []

        let cond = new ConditionObj()
        if (this.drawType == 1) {
            cond.type = ConditionType.PROP
            cond.id = ItemID.TICKET
            cond.num = 1
        }
        else if (this.drawType == 2) {
            cond.type = ConditionType.DIAMOND
            cond.num = cfgHelper.getMiscData('jackPotPrice')
            if (this.isDiamondDiscount) {
                cond.num = Math.round(cond.num * cfgHelper.getMiscData('jackPotDiscount'))
            }
        }
        add.push(cond)
        if (this.isConvertIntoFragments) {
            const cond = new ConditionObj().init(ConditionType.CHARACTER_FRAG, this.drawPid, 1)
            deduct.push(cond)
        }
        else {
            deductFn.push(() => {
                gameHelper.passenger.deletePasenger(this.drawPid)
            })
        }
        return { add, deduct, deductFn }
    }
}


export class EquipMakeEvent extends EventBase {
    public type: proto.TimeStoneEvent = proto.TimeStoneEvent.TypeEquipMake
    public uid: string
    public beanId: string

    public from(data: proto.IEquipMakeEventData) {
        this.uid = data.uid
        this.beanId = data.beanId
        this.expired = data.expired
    }

    public logic() {
        const add: ConditionObj[] = []
        const deduct: ConditionObj[] = []
        const deductFn: Function[] = []

        deductFn.push(() => {
            gameHelper.equip.delEquip(this.uid)
        })
        const beanCfg = assetsMgr.getJsonData<EquipMakeCfg>('EquipMake', this.beanId)
        add.push(...gameHelper.toConditions(beanCfg.cost))
        return { add, deduct, deductFn }
    }
}

export class BlackHoleBattleEvent extends EventBase {
    public type: proto.TimeStoneEvent = proto.TimeStoneEvent.TypeBlackHoleBattle
    public layer: number
    public level: number
    public curId: string

    public from(data: proto.IBlackHoleBattleEventData) {
        this.curId = data.curId
        this.layer = data.layer
        this.level = data.level
        this.expired = data.expired
    }

    public logic(data: google.protobuf.IAny) {
        const add: ConditionObj[] = []
        const deduct: ConditionObj[] = []
        const deductFn: Function[] = []

        const extra = proto.BlackHoleBattleEventData.decode(data.value)
        deduct.push(...gameHelper.toConditions(extra.node.reward))
        const blackHole = gameHelper.blackHole
        blackHole.curId = extra.curId
        blackHole.nextId = extra.nextId
        blackHole.updateRoles(extra.roles)
        blackHole.updateBuffs(extra.buffs)
        blackHole.updateAids(extra.aids)
        blackHole.updateEquips(extra.equips)
        blackHole.updateDeads(extra.deads)
        blackHole.updateTeam(extra.team)
        const map = blackHole.getMap()
        const old = extra.node
        const matchNode = map.find(node => node.id == old.id)
        matchNode.init(old, blackHole.getLevel())
        return { add, deduct, deductFn }
    }
}

/* 公共声明映射数据 */
const EventMap: { [key: number]: new () => EventBase } = {
    [proto.TimeStoneEvent.TypeJackpot]: JackpotEvent,
    [proto.TimeStoneEvent.TypeEquipMake]: EquipMakeEvent,
    [proto.TimeStoneEvent.TypeBlackHoleBattle]: BlackHoleBattleEvent,
}

const EventProtoMap: { [key: number]: { decode(reader: Uint8Array, length?: number): any } } = {
    [proto.TimeStoneEvent.TypeJackpot]: proto.JackpotEventData,
    [proto.TimeStoneEvent.TypeEquipMake]: proto.EquipMakeEventData,
    [proto.TimeStoneEvent.TypeBlackHoleBattle]: proto.BlackHoleBattleEventData,
}