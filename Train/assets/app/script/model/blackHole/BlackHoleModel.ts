import { game<PERSON>el<PERSON> } from "../../common/helper/GameHelper"
import { BattleTeam } from "../battle/BattleMgr"
import EventType from "../../common/event/EventType"
import BattleRole from "../battle/BattleRole"
import PassengerModel from "../passenger/PassengerModel"
import { Msg } from "../../../proto/msg-define"
import { viewHelper } from "../../common/helper/ViewHelper"
import { util } from "../../../core/utils/Utils"
import { BlackHoleEquipLevelCfg, BlackHoleLayerCfg } from "../../common/constant/DataType"
import { BlackHoleNodeType, PassengerAttr, BlackHoleBuffType, UIFunctionType, BlackHoleEquipTarget, ROLE_ATTR_ID, MarkNewType, NPC_ID } from "../../common/constant/Enums"
import ConditionObj from "../common/ConditionObj"
import BlackHoleEquip, { BlackHoleEquipSkill } from "./BlackHoleEquip"
import { BattleSkillEffectType, BattleSkillTriggerType } from "../battle/BattleEnum"
import BattleSkill from "../battle/BattleSkill"
import { cfgHelper } from "../../common/helper/CfgHelper"

export class BlackHoleNode {
    public id: string = null
    public type: BlackHoleNodeType = null
    public enemies: BattleRole[] = []
    public buffs: BlackHoleBuff[] = []
    public equips: BlackHoleEquip[] = []
    public aids: BattleRole[] = []
    public isFog: boolean = false
    public rewards: ConditionObj[] = []
    public layer: number = 0
    public layerIndex: number = 0
    public level: number = 0
    public cfg: BlackHoleLayerCfg = null
    public nexts: string[] = []

    public pos0: number = 0
    public pos1: number = 0
    public zIndex: number = 0

    public init(data: proto.IBlackHoleNode, level: number) {
        this.id = data.id
        this.level = level
        this.type = data.type
        this.isFog = data.isFog
        this.rewards = gameHelper.toConditions(data.reward)
        let [a, b] = this.id.split("-")
        this.layer = Number(a)
        this.layerIndex = Number(b)
        this.enemies = data.enemies.map(e => {
            return gameHelper.toBattleRole(e)
        })
        this.buffs = data.buffs.map(b => new BlackHoleBuff().init(b))
        this.aids = data.aids.map(e => {
            return gameHelper.toBattleRole(e)
        })
        this.equips = data.equips.map(e => new BlackHoleEquip().init(e))
        this.cfg = this.getCfg(this.layer)
        this.initNexts()
        return this
    }

    public isNext(id: string) {
        //如果战斗，然后输了，之后也只能打那个节点了
        return this.nexts?.has(gameHelper.blackHole.nextId) ? id == gameHelper.blackHole.nextId : this.nexts?.has(id)
    }

    public isBoss() {
        if (this.nexts.length != 1) return
        for (let next of this.nexts) {
            let node = gameHelper.blackHole.getNode(next)
            if (node.type == BlackHoleNodeType.END) return true
        }
        return false
    }

    public isEnd() {
        return this.type == BlackHoleNodeType.END
    }

    public getCfg(layer) {
        return assetsMgr.checkJsonData<BlackHoleLayerCfg>("BlackHoleLayer", `${this.level}-${layer}`)
    }

    public initNexts() {
        let curLayer = this.cfg
        let nextLayer = this.getCfg(this.layer + 1)
        if (!nextLayer) return
        let nexts = []
        let l = Math.max(1, this.layerIndex - 1), r = Math.min(nextLayer.count, this.layerIndex)
        if (curLayer.count <= nextLayer.count) {
            l = this.layerIndex, r = Math.min(nextLayer.count, this.layerIndex + 1)
        }
        // console.log(this.id, l, r)
        for (let i = l; i <= r; i++) {
            let id = `${nextLayer.layer}-${i}`
            nexts.push(id)
        }
        this.nexts = nexts
    }
    public getRewards() { return this.rewards }
}

export class BlackHoleBuff {
    public type: number = 0
    public targets: string[] = []
    public add: { [key: string]: number }

    public init(data: proto.IBlackHoleBuff) {
        this.type = data.type
        this.targets = data.targets || []
        this.add = data.add || {}
        return this
    }

    public get name() {
        return `name_blackHoleItem_${this.type}`
    }

    public get content() {
        return `content_blackHoleItem_${this.type}`
    }
}

@mc.addmodel("blackHole")
export default class BlackHoleModel extends mc.BaseModel {

    private map: BlackHoleNode[] = []
    public curId: string = ""
    public nextId: string = ""

    public team: BattleTeam = null
    public roles: BattleRole[] = []
    private buffs: BlackHoleBuff[] = []
    private deads: string[] = []
    private aids: BattleRole[] = []
    private level: number = 1
    private bossMap: { [key: number]: proto.IBattleRole[] } = {}
    public equips: BlackHoleEquip[] = []
    public isUnlock: boolean = false

    public data: proto.IBlackHole = null

    public currency: number = 0 //星海币

    public add: number = 1 //装备加成

    public isInit() {
        return this.map.length > 0
    }

    public init() {
        this.updateInfo(this.data)
    }

    public updateInfo(data: proto.IBlackHole) {
        this.level = data.level
        this.isUnlock = data.isUnlock

        this.add = data.add || 1

        this.updateRoles(data.roles)

        this.map = data.map.map((_data) => {
            return new BlackHoleNode().init(_data, data.level)
        })
        this.initMapPos()

        this.curId = data.curId
        this.nextId = data.nextId

        this.updateTeam(data.team)
        this.updateBuffs(data.buffs)
        this.updateDeads(data.deads)
        this.updateAids(data.aids)
        this.updateEquips(data.equips)

        for (let boss of data.bosses) {
            this.bossMap[boss.level] = boss.roles
        }

        this.currency = data.currency
    }

    public updateRoles(roles: proto.IBattleRole[]) {
        this.roles = roles.map(d => {
            return gameHelper.toBattleRole(d, false)
        })
    }

    public updateBuffs(buffs: proto.IBlackHoleBuff[]) {
        this.buffs = buffs.map(d => new BlackHoleBuff().init(d))
    }

    public updateAids(aids: proto.IBattleRole[]) {
        this.aids = aids.map(d => { return gameHelper.toBattleRole(d) })
    }

    public updateEquips(equips: proto.IBlackHoleEquip[]) {
        this.equips = equips.map(e => new BlackHoleEquip().init(e))
    }
    public updateTeam(team: proto.IBattleTeam) {
        if (!this.team) {
            this.team = new BattleTeam()
        }
        this.team.init(team)
    }
    public updateDeads(deads: string[]) {
        this.deads = deads
    }

    public initMapPos() {
        if (this.map.length <= 0) return
        this.map[0].zIndex = 1000
        for (let node of this.map) {
            for (let index in node.nexts) {
                let next = this.getNode(node.nexts[index])
                if (!next) {
                    console.log(node, index)
                }
                next.zIndex = node.zIndex - (+index == 0 ? 10 : 9) //每层最多10个
                next.pos0 = node.pos0 + (+index == 0 ? 1 : 0)
                next.pos1 = node.pos1 + (+index == 0 ? 0 : 1)
            }
        }
    }

    public getMap() { return this.map }
    public getCurNode() { return this.getNode(this.curId) }
    public getNode(id: string) {
        return this.map.find(n => n.id == id)
    }

    public getBossNode() {
        return this.map.find(n => n.isBoss())
    }

    public isStart() {
        return this.roles.length > 0
    }

    public isEnd() {
        return this.getCurNode()?.type == BlackHoleNodeType.END
    }

    // 全部通关
    public isRoundEnd() { return this.isEnd() && this.getUnlockMaxLevel() == this.getLevel() }

    public getUnlockMaxLevel() {
        const ary = assetsMgr.getJson<any>("BlackHoleLevel").datas
        return ary.filter(i => i.equip?.length).pop().id
    }

    public getRemainNodes() {
        let que: string[] = []
        let cur = this.nextId ? this.getNode(this.nextId) : this.getCurNode()
        let index = 0
        que.push(cur.id)
        while (que.length > index) {
            let now = this.getNode(que[index])
            if (!now) break
            index++
            now.nexts?.forEach((next) => {
                if (!que.has(next)) {
                    que.push(next)
                }
            })
        }
        if (this.nextId) que.push(this.curId)
        return que
    }

    public async readyStart(level: number) {
        let msg = new proto.C2S_ReadyStartBlackHoleMessage({ level })
        const _res = await gameHelper.net.request(Msg.C2S_ReadyStartBlackHoleMessage, msg, true)
        const res = proto.S2C_ReadyStartBlackHoleMessage.decode(_res)
        const { code, boss } = res
        if (code == 0) {
            this.bossMap[level] = boss
            return true
        }
        else {
            viewHelper.showNetError(code)
        }

    }

    public async start(level: number, roles: number[] = []) {
        let msg = new proto.C2S_StartBlackHoleMessage({ level, roles })
        const _res = await gameHelper.net.request(Msg.C2S_StartBlackHoleMessage, msg, true)
        const res = proto.S2C_StartBlackHoleMessage.decode(_res)
        const { code, blackHole } = res
        if (code == 0) {
            this.updateInfo(blackHole)
            return true
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    @util.addLock
    public async select(data: proto.IC2S_SelectBlackHoleNodeMessage) {
        let node = this.getNode(data.nodeId)
        let msg = new proto.C2S_SelectBlackHoleNodeMessage({ nodeId: data.nodeId, buff: data.buff, aid: data.aid, battle: data.battle, equip: data.equip })
        const _res = await gameHelper.net.request(Msg.C2S_SelectBlackHoleNodeMessage, msg, true)
        const res = proto.S2C_SelectBlackHoleNodeMessage.decode(_res)
        const { code, rebirth, battle, buff, curId, nextId, equips } = res
        if (code == 0) {
            if (data.aid) {
                let aid = node.aids.find(p => p.uid == data.aid)
                this.addAid(aid)
            }
            if (rebirth) {
                this.reBirth(rebirth)
            }
            if (buff) {
                this.buffs.push(new BlackHoleBuff().init(buff))
            }
            if (data.equip) {
                this.equips.push(new BlackHoleEquip().init(data.equip))
            }
            if (equips?.length) {
                node.equips = equips.map(e => new BlackHoleEquip().init(e))
            }
            let getReward = false
            if (battle) {
                let isWin = battle.isWin
                let lives = battle.uids
                let deads = []
                let roles = this.team.getRoles()
                if (isWin) {
                    roles.for((uid, i) => {
                        if (uid && !lives.has(uid)) {
                            deads.push(uid)
                            roles.splice(i, 1)
                        }
                    })
                    node.enemies = []
                    getReward = true

                    if (node.buffs.length || equips?.length) {
                        node.type = BlackHoleNodeType.ATTR
                    }
                }
                else {
                    // deads = roles.filter(uid => !!uid)
                    // this.team.setRoles([])
                    // node.enemies = node.enemies.filter(e => lives.has(e.uid))
                }
                this.deads.pushArr(deads)
            }
            if (node.isEnd()) {
                getReward = true
            }
            if (getReward && (node.rewards?.length)) {
                gameHelper.grantRewards(gameHelper.toConditions(node.rewards))
            }
            this.curId = curId
            this.nextId = nextId

            return res
        }
        else {
            viewHelper.showNetError(code)
            return res
        }
    }

    public addDead(uid: string) {
        this.deads.push(uid)
    }

    public reBirth(uid: string) {
        this.deads.remove(uid)
    }

    private getBuffAttr(attr: PassengerAttr, uid: string) {
        let sum = 0
        for (let buff of this.buffs) {
            if (!buff.targets.has(uid) && buff.type != BlackHoleBuffType.ALL) continue
            sum += (buff.add[attr] || 0)
        }
        return sum
    }

    public getPassengers() {
        return this.roles.concat(this.aids).map(r => {
            let uid = r.uid, id = r.id
            let addAttr = {
                [PassengerAttr.ATTACK]: this.getBuffAttr(PassengerAttr.ATTACK, uid),
                [PassengerAttr.HP]: this.getBuffAttr(PassengerAttr.HP, uid),
            }
            let hp = r.hp + addAttr[PassengerAttr.HP]
            let attack = r.attack + addAttr[PassengerAttr.ATTACK]

            let skills = r.getSkills()
            let equips = this.equips.filter(e => {
                if (e.cfg.target == BlackHoleEquipTarget.ANIMAL_TYPE) return e.target == r.animalType
                if (e.cfg.target == BlackHoleEquipTarget.BATTLE_TYPE) return e.target == r.battleType
                return e.cfg.target == BlackHoleEquipTarget.ALL
            })
            let skillInc = 0
            let equipSkills = []
            for (let equip of equips) {
                let skill = equip.skill
                if (skill.trigger.type == BattleSkillTriggerType.NONE) {
                    hp += skill.effects.find(e => e.type == BattleSkillEffectType.CHANGE_HP)?.value || 0
                    attack += skill.effects.find(e => e.type == BattleSkillEffectType.CHANGE_ATTACK)?.value || 0
                    skillInc += skill.effects.find(e => e.type == BattleSkillEffectType.CHANGE_SKILL)?.value || 0
                }
                else if (skill.trigger.type == BattleSkillTriggerType.BATTLE_AFTER) {
                }
                else if (equip.sender) {
                    let skill = new BlackHoleEquipSkill().init(equip.id, equip.level, r, equip.target)
                    equipSkills.push(skill)
                }
            }

            if (skillInc > 0) {
                skills = skills.map(s => {
                    let newSkill = s.clone()
                    return newSkill.init(s.getId(), s.getLevel() + skillInc, s.getRole())
                })
            }
            if (equipSkills.length > 0) {
                skills = skills.concat(equipSkills)
            }
            return new BattleRole().initData({ uid, id, attack, hp, skills, lv: r.lv, starLv: r.starLv, role: r.role })
        })
    }

    public getEquipBuff() {
        let skills = this.equips.filter(e => {
            return e.skill.trigger.type != BattleSkillTriggerType.NONE && e.skill.trigger.type != BattleSkillTriggerType.BATTLE_AFTER
        }).map(equip => {
            return equip.skill
        })
        let r = new BattleRole().initData({ id: Number(ROLE_ATTR_ID.BLACK_HOLE), skills })
        for (let skill of skills) {
            skill.setRole(r)
        }
        return r
    }

    public getLives() {
        return this.getPassengers().filter(r => !this.deads.has(r.uid))
    }

    public getDeads() {
        return this.getPassengers().filter(r => this.deads.has(r.uid))
    }

    public needRebirth() {
        return this.deads.length > 0
    }

    public addAid(role: BattleRole) {
        this.aids.push(role)
    }

    public getAid(uid: string) {
        return this.aids.find(r => r.uid == uid)
    }

    public checkSync() {
        if (!this.isInit()) {
            return this.sync()
        }
        return true
    }

    public async sync() {
        let msg = new proto.C2S_SyncBlackHoleMessage()
        const res = await gameHelper.net.request(Msg.C2S_SyncBlackHoleMessage, msg)
        const { code, blackHole } = proto.S2C_SyncBlackHoleMessage.decode(res)
        if (code == 0) {
            this.updateInfo(blackHole)
            return true
        }
        return false
    }

    public getLevel() {
        return this.level
    }

    public getBoss(level: number) {
        return this.bossMap[level]
    }

    public changeCurrency(val) {
        this.currency += val
        eventCenter.emit(EventType.UPDATE_CURRENCY)
    }

    public getCurrency() {
        return this.currency
    }

    public isSimple() {
        return this.level <= 1
    }

    public getAvgStarRate() {
        let sum = 0
        let roles = this.roles
        for (let role of roles) {
            sum += role.starLv
        }
        return 1 + sum / roles.length * cfgHelper.getStarLvAttrRatio()
    }

    public async unlock() {
        let msg = new proto.C2S_UnlockBlackHoleMessage()
        const res = await gameHelper.net.request(Msg.C2S_UnlockBlackHoleMessage, msg)
        const { code, blackHole } = proto.S2C_UnlockBlackHoleMessage.decode(res)
        if (code == 0) {
            this.updateInfo(blackHole)
            gameHelper.new.pushNew(MarkNewType.NPC_DIALOG, [NPC_ID.BLACK_HOLE_SHOP])
            eventCenter.emit(EventType.UNLOCK_FUNTION, UIFunctionType.PLAY_BLACKHOLE)
            return true
        }
        else {
            viewHelper.showNetError(code)
        }
        return false
    }

}