const { ccclass, property } = cc._decorator;

@ccclass
export default class SpineAABBHitTestCmpt extends cc.Component {
    @property(sp.Skeleton)
    skel: sp.Skeleton = null

    onLoad() {
        this.skel = this.skel || this.getComponent(sp.Skeleton)
        if (!this.skel) return

        this.node.setHitTest((local: cc.Vec2) => {
            let rect = this.skel.getBounds()
            return rect.contains(local)
        })
    }
}


