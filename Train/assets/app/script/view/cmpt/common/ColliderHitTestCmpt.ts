const { ccclass, property } = cc._decorator;

@ccclass
export default class ColliderHitTestCmpt extends cc.Component {

    @property(cc.Node)
    private target: cc.Node = null

    @property(cc.PolygonCollider)
    private collider: cc.PolygonCollider = null

    onLoad() {
        this.collider = this.collider || this.Component(cc.PolygonCollider)
        this.target = this.target || this.node
        if (!this.collider || !this.target) return

        this.target.setHitTest((local: cc.Vec2) => {
            const points = this.collider.points.map(p => p.add(this.collider.offset))
            return cc.Intersection.pointInPolygon(local, points)
        })
    }
}