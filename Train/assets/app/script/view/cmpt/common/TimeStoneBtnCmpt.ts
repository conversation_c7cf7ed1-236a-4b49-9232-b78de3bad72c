import EventType from "../../../common/event/EventType"
import { gameHelper } from "../../../common/helper/GameHelper"
import { viewHelper } from "../../../common/helper/ViewHelper"
import { Equip } from "../../../model/equip/EquipModel"
import { ExtractData } from "../../../model/jackpot/JackpotModel"


const { ccclass, property } = cc._decorator

@ccclass
export default class TimeStoneBtnCmpt extends cc.Component {
    private type: proto.TimeStoneEvent = proto.TimeStoneEvent.TypeNone

    get cost() {
        return assetsMgr.getJsonData<any>("TimeStoneEvent", this.type).cost
    }

    protected onLoad(): void {
        this.node.off("click")
    }

    protected onEnable() {
        this.playEnter()
    }


    protected onDisable() {
        mc.lockTouch(false)
    }


    private async playEnter() {
        mc.lockTouch(true)
        const lvNode = this.node.Child("level")
        lvNode.opacity = 0
        const sk = this.node.Child("sp", sp.Skeleton)
        await sk.playAnimation("chuxian", false)
        sk.playAnimation("daiji", true)
        lvNode.setLocaleKey("common_guiText_11", gameHelper.timeStone.getLv())
        cc.Tween.stopAllByTarget(lvNode)
        cc.tween(lvNode).to(0.5, { opacity: 255 }).start()
        mc.lockTouch(false)
    }

    public initJackpot(ary: ExtractData[]) {
        this.type = proto.TimeStoneEvent.TypeJackpot
        this.node.off("click")
        this.node.on("click", () => {
            if (ary.length == 1) {
                return viewHelper.showPnl("timeStone/TimeStoneUseCommonPnl", {
                    desc: "timeStone_guiText_11", cost: this.cost, callback: async () =>
                        await eventCenter.req(EventType.TIMESTONE_USE, { evts: [0], type: this.type, ids: [0] })
                })
            }
            viewHelper.showPnl("timeStone/TimeStoneUseJackpotPnl", ary)
        })
    }


    public initEquipMake(equip: Equip) {
        this.type = proto.TimeStoneEvent.TypeEquipMake
        this.node.off("click")
        this.node.on("click", () => {
            return viewHelper.showPnl("timeStone/TimeStoneUseCommonPnl", {
                desc: "timeStone_guiText_12", cost: this.cost, callback: async () =>
                    await eventCenter.req(EventType.TIMESTONE_USE, { evts: [0], type: this.type, equip })
            })
        })
    }

    public initBlackholeBattle() {
        this.type = proto.TimeStoneEvent.TypeBlackHoleBattle
        this.node.off("click")
        this.node.on("click", () => {
            return viewHelper.showPnl("timeStone/TimeStoneUseCommonPnl", {
                desc: "timeStone_guiText_13", cost: this.cost, callback: async () =>
                    await eventCenter.req(EventType.TIMESTONE_USE, { evts: [0], type: this.type })
            })
        })
    }
}