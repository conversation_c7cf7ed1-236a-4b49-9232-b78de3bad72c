import { util } from "../../../core/utils/Utils";
import { BATTLE_PREVIEW_BG } from "../../common/constant/Constant";
import { BlackHoleLayerCfg, ItemCfg } from "../../common/constant/DataType";
import { BattleLevelType, BlackHoleNodeType, ConditionType, HeroAnimation, ROLE_ATTR_ID, RuleType, TipsNotType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BattleRole from "../../model/battle/BattleRole";
import BlackHoleModel, { BlackHoleNode } from "../../model/blackHole/BlackHoleModel";
import PlanetTitleCmpt from "../cmpt/planet/PlanetTitleCmpt";

const { ccclass } = cc._decorator;

const VEC0 = new cc.Vec2(134, 186)
const VEC1 = new cc.Vec2(420, 55)


@ccclass
export default class BlackHolePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected mapSv_: cc.ScrollView = null // path://map_sv
    protected roleNode_: cc.Node = null // path://role_n
    protected uiNode_: cc.Node = null // path://ui_n
    protected PlanetTitleNode_: cc.Node = null // path://ui_n/PlanetTitle_n
    protected equipNode_: cc.Node = null // path://ui_n/btns/equip_be_n
    //@end

    private cb: Function = null

    private model: BlackHoleModel = null
    private lastCur: BlackHoleNode = null

    public listenEventMaps() {
        return [
            { [EventType.BLACK_HOLE_MOVE]: this.refresh },
            { [EventType.DAILY_REFRESH]: this.onDailyRefresh }
        ]
    }

    public async onCreate() {
        this.setParam({ isMask: false, isAct: false })
        this.model = gameHelper.blackHole
        await this.model.checkSync()
    }

    public onEnter(cb: any) {
        this.cb = cb
        this.init()

        this.equipNode_.active = !gameHelper.blackHole.isSimple()
        viewHelper.activeMainWind(false)

        let content = this.mapSv_.content
        // 计算listNew中所有item的位置范围
        let maxX = -Infinity, maxY = -Infinity
        content.Child("listNew").children.forEach((item) => {
            if (!item.active) return
            let pos = item.getPosition()
            let size = item.getContentSize()
            maxX = Math.max(maxX, pos.x + size.width / 2)
            maxY = Math.max(maxY, pos.y + size.height / 2)
        })
        // 设置content大小，添加一些padding
        content.setContentSize(
            maxX + 500,
            maxY + 400,
        )

        let bgNode = this.mapSv_.content.Child("bg")
        bgNode.scale = Math.max(content.width / bgNode.width, content.height / bgNode.height)

        if (this.model.getLevel() == 1 && this.model.getCurNode().layer < 2) {
            viewHelper.showPlanetTips("explore_guiText_21", TipsNotType.NORMAL_2)
        }
    }

    onRemove() {
        !!this.cb && this.cb()
        viewHelper.activeMainWind(true)
    }

    protected update(dt: number): void {
        let time = ut.millisecondFormat(gameHelper.world.getNextDaySurpluTime(), "hh:mm:ss")
        this.uiNode_.Child("resttime/time", cc.RichText).setLocaleKey("blackHole_guiText_3", `<color=79ee73>${time}</c>`)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://ui_n/back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://ui_n/btns/passengers_be
    onClickPassengers(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("blackHole/BlackHoleRole")
    }

    // path://ui_n/btns/equip_be_n
    onClickEquip(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("blackHole/BlackHoleEquip")
    }

    // path://ui_n/btns/boss_be
    onClickBoss(event: cc.Event.EventTouch, data: string) {
        this.onPreviewBoss()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    //新：只在init里生成一遍地图，init只调用一遍
    //维护lastcur,cur来判断 1.角色是否跳跃；2.地块是否消失；3.
    private async init() {
        let cur = gameHelper.blackHole.getCurNode()
        let map = gameHelper.blackHole.getMap()
        let curNode: cc.Node = null
        let nodeIds = gameHelper.blackHole.getRemainNodes()
        this.mapSv_.content.Child("listNew").Items(map, (it, data) => {
            it.Data = data
            if (data == cur) {
                curNode = it
            }
            if (nodeIds.has(data.id)) {
                it.active = true
                this.setItem(it, data)
            }
            else {
                it.active = false
            }
        })

        this.mapSv_.node.Child('view/content').setPosition(cc.v2(-curNode.x + 500, -curNode.y + 400))
        this.PlanetTitleNode_.Component(PlanetTitleCmpt).init('blackHole_guiText_1', '', RuleType.BLACKHOLE)
        this.roleNode_.parent = curNode.Child('bg', sp.Skeleton).getAttachedNode('guadian')
        this.roleNode_.setPosition(cc.v2(0, -50))
        resHelper.loadOwnRoleSp(1005, this.roleNode_, this.getTag())
        this.lastCur = cur
    }

    //操作节点方法： 1.玩家是否需要从lastcur跳到cur；2.地块是否需要消失/变色；3.迷雾是否需要消散
    private async refresh() {
        let cur = gameHelper.blackHole.getCurNode()
        let curNode: cc.Node = null
        //------------------------------是否需要跳跃-------------------------------------------
        if (!!this.lastCur && this.lastCur != cur && cur.type != BlackHoleNodeType.END) {
            this.mapSv_.content.Child('listNew').children.forEach((it) => {
                let data = it.Data
                if (data == cur) {
                    curNode = it
                }
            })
            let guadian = curNode.Child('bg', sp.Skeleton).getAttachedNode('guadian')
            let roleSk = this.roleNode_.Component(sp.Skeleton)
            guadian.Child('detail').active = false
            await roleSk.playAnimation(HeroAnimation.JUMP2, false)
            await roleSk.playAnimation(HeroAnimation.JUMP3, false)
            let targetPos = ut.convertToNodeAR(guadian, this.roleNode_.parent, cc.v2(0, -50))
            cc.tween(this.roleNode_)
                .then(cc.jumpTo(roleSk.getAnimationDuration(HeroAnimation.JUMP4), targetPos, 250, 1))
                .call(() => {
                    this.roleNode_.parent = guadian
                    this.roleNode_.setPosition(cc.v2(0, -50))
                    roleSk.playAnimation(HeroAnimation.JUMP4, false).then(() => {
                        this.refreshMap()
                        this.lastCur = cur
                        roleSk.playAnimation('aniIdle')
                    })
                }).start()
        }
        else {
            this.refreshMap()
            this.lastCur = cur
        }
    }

    private refreshMap() {
        //------------------------------地块是否需要消失&迷雾是否需要消散-------------------------------------------
        let cur = gameHelper.blackHole.getCurNode()
        let curNode: cc.Node = null
        let lastNode = null
        let nodeIds = gameHelper.blackHole.getRemainNodes()
        this.mapSv_.content.Child("listNew").children.forEach((it) => {
            let data = it.Data
            if (cur == data) {
                curNode = it
            }
            if (this.lastCur == data) {
                lastNode = it
            }

            if (cur.type == BlackHoleNodeType.END && !!this.lastCur) {
                it.Component(cc.Button).interactable = false
                if (data == this.lastCur) {
                    it.active = true
                }
                else if (data == cur) {
                    it.Child('bg', sp.Skeleton).playAnimation('aniHide', false).then(() => {
                        it.active = false
                    })
                }
                else {
                    it.active = false
                }
            }
            else if (it.active && !nodeIds.has(data.id)) {
                it.Child('bg', sp.Skeleton).getAttachedNode('guadian').Child('detail').active = false
                it.Child('bg', sp.Skeleton).getAttachedNode('guadian').Child('fog').active = false
                it.Child('bg', sp.Skeleton).playAnimation('aniHide', false).then(() => {
                    this.stopAnimation(it)
                    it.active = false
                })
            }
            else {
                this.setItem(it, data)
            }
        })
        if (cur.type == BlackHoleNodeType.END && !!this.lastCur) {
            this.roleNode_.parent = lastNode.Child('bg', sp.Skeleton).getAttachedNode('guadian')
            this.roleNode_.setPosition(cc.v2(0, -50))
        }
        else {
            this.roleNode_.parent = curNode.Child('bg', sp.Skeleton).getAttachedNode('guadian')
            this.roleNode_.setPosition(cc.v2(0, -50))
        }
    }

    //item的active为false前停止子节点正在播放的动画（会报警告）
    private stopAnimation(it: cc.Node) {
        let node: cc.Node = it.Child('bg', sp.Skeleton).getAttachedNode('guadian')
        let detail = node.Child('detail')
        let fog = node.Child('fog')
        if (fog.active) {
            fog.Component(sp.Skeleton).paused = true
        }
        if (detail.active) {
            if (detail.Child('effect').active) {
                detail.Child('effect').children.forEach((sk) => {
                    if (sk.active) { sk.Component(sp.Skeleton).paused = true }
                })
            }
            if (detail.Child('monster').active) {
                detail.Child('monster/sp', sp.Skeleton).paused = true
            }
        }

    }

    //这里需要改成纯粹的设置节点（没有动画演绎）
    private setItem(it: cc.Node, data: BlackHoleNode) {
        let cur = gameHelper.blackHole.getCurNode()
        let isNext = cur.isNext(data.id)
        let isPreview = !isNext
        //----------------设位置-------------------
        let x = data.pos0 * VEC0.x + data.pos1 * VEC1.x + 500
        let y = data.pos0 * VEC0.y + data.pos1 * VEC1.y + 400
        it.setPosition(new cc.Vec2(x, y))
        if (data.type == BlackHoleNodeType.END && cur != data) {
            it.x += 150
            it.y += 50
        }
        it.zIndex = data.zIndex
        //----------------设置属性------------------

        let type = data.type
        let isFog = data.isFog && isPreview
        let needFog = data.isFog && !isPreview && !!this.lastCur && this.lastCur != cur //需要迷雾消失演绎
        let bgSk = it.Child('bg', sp.Skeleton)
        let node: cc.Node = it.Child('bg', sp.Skeleton).getAttachedNode('guadian')
        bgSk.playAnimation("jingzhi", true)
        let skin = "skin_01"
        let isBoss = data.isBoss()
        if (isNext || data == cur || type == BlackHoleNodeType.END) { //高亮皮肤
            if (isBoss) {
                skin = "skin_05"
            }
            else {
                skin = "skin_03"
            }
        }
        else if (data.isBoss()) {
            skin = "skin_04"
        }
        bgSk.setSkin(skin)
        let detail = node.Child('detail')
        let fog = node.Child('fog')
        if (cur == data) {
            detail.active = false
            fog.active = false
            it.Component(cc.Button).interactable = false
            return
        }

        it.Component(cc.Button).interactable = true
        if (isFog) {
            detail.active = false
            fog.active = true
            fog.Component(sp.Skeleton).playAnimation('jingzhi', true)
        }
        else {
            fog.active = false
            detail.active = true
            if (needFog) {
                fog.active = true
                detail.opacity = 0
                fog.Component(sp.Skeleton).playAnimation(data.type == BlackHoleNodeType.BATTLE ? "aniHide" : "aniHide2", false).then(() => {
                    cc.tween(detail).to(0.2, { opacity: 255 }).call(() => {
                        fog.active = false
                    }).start()
                })
            }
            if (type == BlackHoleNodeType.START) {
                detail.active = false
            }
            else if (type == BlackHoleNodeType.END || type == BlackHoleNodeType.BOX) {
                detail.Swih('chest')
                let chest = detail.Child('chest')
                let layerCfg = assetsMgr.getJsonData<BlackHoleLayerCfg>("BlackHoleLayer", `${gameHelper.blackHole.getLevel()}-${data.layer}`)
                let cfg = assetsMgr.getJsonData<ItemCfg>("Item", layerCfg.box_icon)
                resHelper.loadIcon(chest, 'chest', cfg.icon, this.getTag())
                chest.off('click')
                chest.on('click', () => {
                    if (isPreview) {
                        viewHelper.showAlert("blackHole_tips_1")
                    }
                    else {
                        this.showBox(it, data)
                    }
                })
            }
            else if (type == BlackHoleNodeType.BATTLE) {
                detail.Swih('monster')
                let monster = detail.Child('monster')
                if (data.enemies.length > 0) {
                    let sk = monster.Child('sp')
                    resHelper.loadRoleSp(data.enemies[0]?.id, sk, this.getTag()).then(() => {
                        if (!cc.isValid(sk)) return
                        uiHelper.setRoleButtonSize(monster, sk)
                    })
                    sk.scale = isBoss ? 1.1 : 1
                    monster.Child('lv', cc.Label).setLocaleKey('common_guiText_11', data.enemies[0]?.lv)
                    monster.off('click')
                    monster.on('click', () => {
                        if (!gameHelper.blackHole.getCurNode().isNext(data.id)) return
                        if (isPreview) {
                            viewHelper.showAlert("blackHole_tips_1")
                        }
                        else {
                            this.showBattle(data, isPreview)
                        }
                    })
                }
            }
            else {
                detail.Swih('effect')
                detail.Child('effect').Swih(`${this.getDesc(type)}`)
                let effectNode = detail.Child(`effect/${this.getDesc(type)}`, sp.Skeleton)
                effectNode.playAnimation(isPreview ? 'jingzhi' : 'aniIdle', true)
            }
        }
        //----------------绑定按钮------------------
        if (isPreview) {
            it.off("click")
            it.on("click", () => {
                viewHelper.showAlert("blackHole_tips_1")
            })
        }
        else {
            it.off("click")
            it.on("click", () => {
                if (!gameHelper.blackHole.getCurNode().isNext(data.id)) return
                if (type == BlackHoleNodeType.BATTLE) {
                    this.showBattle(data)
                }
                else if (type == BlackHoleNodeType.END) {
                    this.showBox(it, data)
                }
                else {
                    viewHelper.showPnl("blackHole/BlackHoleDetail", data, isPreview)
                }
            })
        }
    }

    private getDesc(type: BlackHoleNodeType) {
        if (type == BlackHoleNodeType.BATTLE) {
            return 3//"战斗"
        }
        else if (type == BlackHoleNodeType.AID) {
            return 2//"援助"
        }
        else if (type == BlackHoleNodeType.ATTR) {
            return 0//"属性增益"
        }
        else if (type == BlackHoleNodeType.REBIRTH) {
            return 1//"复活"
        }
        else if (type == BlackHoleNodeType.END) {
            return 4//"宝箱"
        }
        /*
        else if (type == BlackHoleNodeType.START) {
            return "起点"
        }
        return ""
        */
    }

    private async showBattle(data: BlackHoleNode, isPreview = false) {
        let passengers = gameHelper.blackHole.getLives()
        let monsters = data.enemies
        let team = gameHelper.blackHole.team
        let rewards = data.rewards
        let start = await new Promise(callback => {
            viewHelper.showPnl("battle/BattlePreview", {
                bg: BATTLE_PREVIEW_BG.BLACK_HOLE, title: { key: "name_chapter_blackHole" }, enemies: monsters, isPreview, rewards, callback,
            })
        })
        if (!start) return
        viewHelper.hidePlanetTips()

        let onStartBattle = async () => {
            if (!gameHelper.blackHole.nextId) {
                let res = await gameHelper.blackHole.select({ nodeId: data.id })
                let succ = res.code == 0
                if (succ) {
                    eventCenter.emit(EventType.BLACK_HOLE_MOVE)
                }
                return succ
            }
            return true
        }
        let onWin = async (res) => {
            await gameHelper.blackHole.select({ nodeId: data.id, battle: { isWin: true, uids: res.roles.map(r => r.uid) } })
        }
        let res = await viewHelper.showBattle({
            passengers, monsters, team, isPreview,
            battleBg: resHelper.getBattleBg("blackhole"), isBlackHole: true,
            rewards, onStartBattle, onWin,
            title: { name: 'blackHole_guiText_1', progress: assetsMgr.lang('blackHole_guiText_2', data.layer) },
            buff: gameHelper.blackHole.getEquipBuff(),
            noAgain: true,
            levelType: BattleLevelType.BLACK_HOLE, levelId: `${gameHelper.blackHole.getLevel()}-${data.layer}`,
        })
        if (res?.isWin) {
            eventCenter.emit(EventType.BLACK_HOLE_MOVE)

            if (data.type == BlackHoleNodeType.ATTR) {
                viewHelper.showPnl("blackHole/BlackHoleDetail", data)
            }
        }
    }

    @util.addLock
    private async showBox(it: cc.Node, data: BlackHoleNode) {
        if (data.type == BlackHoleNodeType.END) {
            let x = data.pos0 * VEC0.x + data.pos1 * VEC1.x + 500
            let y = data.pos0 * VEC0.y + data.pos1 * VEC1.y + 400
            await cc.tween(it).to(0.3, { x, y }).promise()
        }
        let { code } = await gameHelper.blackHole.select({ nodeId: data.id })
        if (code != 0) return
        let rewards = gameHelper.toConditions(data.rewards)
        eventCenter.emit(EventType.BLACK_HOLE_MOVE)
        const arg = {
            rewards,
            done: gameHelper.blackHole.isRoundEnd(),
            resolve: () => {
                if (!gameHelper.blackHole.isRoundEnd()) {
                    return void this.gotoNext()
                }
            }
        }

        await viewHelper.showBlackHoleReward(arg)
    }

    private async onPreviewBoss() {
        viewHelper.showPnl("blackHole/BlackHolePreview")
    }

    private async onDailyRefresh() {
        await new Promise(r => {
            viewHelper.showMessageBox("blackHole_tips", r, null, { lockClose: true })
        })
        viewHelper.gotoMainFromPlanet()

    }

    @util.addLock
    private async gotoNext() {
        if (gameHelper.blackHole.getUnlockMaxLevel() == gameHelper.blackHole.getLevel()) return
        // 这里可以有一个过渡动画
        const nextLevel = gameHelper.blackHole.getLevel() + 1
        const succ = await gameHelper.blackHole.start(nextLevel)
        if (!succ) return
        this.lastCur = null
        this.onEnter(this.cb)
    }
}
