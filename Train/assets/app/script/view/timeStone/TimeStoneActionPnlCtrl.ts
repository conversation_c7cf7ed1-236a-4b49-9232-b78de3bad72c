import EventType from "../../common/event/EventType";
import { anim<PERSON>elper } from "../../common/helper/AnimHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class TimeStoneActionPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected liziNode_: cc.Node = null // path://lizi_n
    protected stoneSp_: sp.Skeleton = null // path://stone_sp
    protected tipNode_: cc.Node = null // path://tip_n
    //@end

    private _type: proto.TimeStoneEvent = null
    private _onAnimationEnd: Function = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false, Index: 10000 })
    }

    public onEnter(data: any) {
        this._type = data.type
        this._onAnimationEnd = data.onAnimationEnd
        this.tipNode_.Child("lb").setLocaleKey(`timeStone_topText_${data.type}`)
        this.tipNode_.active = false
        this.liziNode_.active = true
        this.stoneSp_.node.active = true
        viewHelper.showUI(false)
        this.actParticle()
        this.actStone()
    }

    public onRemove() {
        viewHelper.showUI(true)
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private async actParticle() {
        await ut.wait(0.5, this)
        let ps = this.liziNode_.Component(cc.ParticleSystem)
        ps.resetSystem()
        await ut.wait(2.7, this)
        ps.stopSystem()
    }
    private async actStone() {
        let duration = this.stoneSp_.getAnimationDuration("animation")
        this.stoneSp_.playAnimation("animation")
        let delay = duration
        if (this._type == proto.TimeStoneEvent.TypeBlackHoleBattle) {
            delay *= .88
        }
        await ut.wait(delay, this)
        this._onAnimationEnd?.()
        await ut.wait(duration - delay, this)
        this.node.Component(cc.BlockInputEvents).enabled = false
        this.tipNode_.active = true
        this.liziNode_.active = false
        this.stoneSp_.node.active = false
        this.moveIn(this.tipNode_)
        await ut.wait(4, this)
        await this.moveOut(this.tipNode_)
        this.close()
    }

    public fadeOut() {
        cc.tween(this.node).to(0.5, { opacity: 0 }).call(this.close.bind(this)).start()
    }


    private moveIn(it: cc.Node, time: number = 0.3) {
        if (it.Data == null) it.Data = it.y
        let y = it.Data
        animHelper.moveYIn(it, time, y, -y)
    }
    private async moveOut(it: cc.Node, time: number = 0.3) {
        let y = it.Data
        await animHelper.moveYOut(it, time, y, -y)
    }
}
