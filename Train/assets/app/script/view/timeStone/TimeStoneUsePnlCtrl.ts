import { ConditionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { EventBase } from "../../model/stone/TimeStoneModel";

const { ccclass } = cc._decorator;

type res = {
    node: cc.Node,
    time: number
}
@ccclass
export default class TimeStoneUsePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected stoneSp_: sp.Skeleton = null // path://root/stone_sp
    protected levelLbl_: cc.Label = null // path://root/info/level_l
    protected energyNode_: cc.Node = null // path://root/info/energy_n
    protected costNode_: cc.Node = null // path://root/bottom/cost_n
    protected timeListSv_: cc.ScrollView = null // path://root/timeList_sv
    protected liziNode_: cc.Node = null // path://lizi_n
    protected backNode_: cc.Node = null // path://back_be_n
    protected desc1Node_: cc.Node = null // path://desc_1_n
    protected desc2Node_: cc.Node = null // path://desc_2_n
    //@end

    private _cachePool1: cc.Node[] = []
    private _cachePool2: cc.Node[] = []

    private _waitList: res[] = []

    public listenEventMaps() {
        return [
            { [EventType.TIMESTONE_LV_UP]: this.onLvUp },
        ]
    }

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false })
    }

    public onEnter(data: any) {
        this.desc1Node_.active = this.desc2Node_.active = false
        this.initView()
    }

    public onRemove() {
        super.onRemove()
        viewHelper.showUI(true)
    }

    public fadeOut() {
        cc.tween(this.node).to(0.5, { opacity: 0 }).call(this.close.bind(this)).start()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/bottom/upgrade_be
    onClickUpgrade(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("timeStone/TimeStoneLevelUpPnl")
    }

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://name/rule_be
    onClickRule(event: cc.Event.EventTouch, data: string) {
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async onLvUp() {
        this.setLv()
        this.setEnergy()
        this.updateCost()
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.setLv()
        this.setEnergy()
        this.updateCost()

        this.timeListSv_.on
        this.updateTimeList()
    }

    async updateTimeList() {
        this.timeListSv_.node.active = false
        await gameHelper.timeStone.syncData()
        this.timeListSv_.node.active = true
        const ary = gameHelper.timeStone.records
        this.timeListSv_.Items(ary, <T extends EventBase>(it: cc.Node, data: T, index: number) => {
            it.Child("name").setLocaleKey("timeStone_evtText_" + data.type)
            it.Child("type/sub", cc.MultiFrame).setFrame(data.type - 1)
            it.Child("pointLine").active = index != ary.length - 1
            it.Child("name", cc.MultiColor).setColor(data.expired)
            it.Child("type", cc.MultiFrame).setFrame(data.expired)
            it.Child("type/sub").scale = data.expired ? .85 : 1
            it.Child("type/sub").opacity = data.expired ? 153 : 255
            const typeNode = it.Child("type")
            typeNode.off("click")
            typeNode.on("click", () => {
                this.getDetailNode(data, it)

            })
        })
    }

    private setLv() {
        const lv = gameHelper.timeStone.getLv()
        this.levelLbl_.setLocaleKey("common_guiText_11", lv)
    }

    private setEnergy() {
        const energy = gameHelper.timeStone.getEnergy()
        const max = gameHelper.timeStone.getMaxEnergy()
        this.energyNode_.Child("progressRt").setLocaleUpdate(() => `${energy}/${max}`)
        const progress = this.energyNode_.Child("progress")
        progress.Child("mask").width = progress.width * energy / max
        this.energyNode_.Child("desc").setLocaleKey("timeStone_guiText_4", gameHelper.timeStone.getDailyRecover())
    }

    private updateCost() {
        this.costNode_.active = false
    }

    private getDetailNode<T extends EventBase>(data: T, targetNode: cc.Node) {
        let ary: cc.Node[] = null
        let createFn: () => cc.Node = null

        if (!data.expired) {
            ary = this._cachePool1
            createFn = () => cc.instantiate2(this.desc1Node_, this.node)
        }
        else {
            ary = this._cachePool2
            createFn = () => cc.instantiate2(this.desc2Node_, this.node)
        }

        let it = ary.pop()
        if (it == null) {
            it = createFn()
        }
        it.active = true
        it.Child("lbl", cc.Label).setLocaleKey("")


        const pos = ut.convertToNodeAR(targetNode, this.node)
        pos.x += (targetNode.width >> 1) + (it.width >> 1)
        if (!data.expired) {
            pos.x += 20
        }
        it.setPosition(pos)

        this._waitList.push({ node: it, time: 4000 })
    }

    private async clearNode(node: cc.Node) {
        cc.Tween.stopAllByTarget(node)
        await cc.tween(node).to(0.5, { opacity: 0 }).promise()
        node.active = false
        node.setPosition(cc.v2())
        node.opacity = 255
        node.scale = 1
        if (node.name == this.desc1Node_.name) {
            this._cachePool1.push(node)
        }
        else {
            this._cachePool2.push(node)
        }
        return node
    }

    update(dt: number) {
        for (const item of this._waitList) {
            item.time -= dt * 1000
            if (item.time > 0) continue
            this._waitList.remove(item)
            this.clearNode(item.node)
        }
    }

}
