sp.Skeleton.prototype.playAnimation = async function (name: string, loop = false, elapsed: number = 0) {
    try {
        let sk: sp.Skeleton = this
        let max = sk.getAnimationDuration(name)
        if (max <= 0) {
            sk.setAnimation(0, name, loop);
            return true
        }
        if (loop) {
            elapsed %= max
        }
        let off = max - elapsed
        sk.setAnimation(0, name, loop);
        if (off <= 0) {
            if (max > 0) {
                sk.addTime(max)
            }
            return true
        }
        if (elapsed > 0) {
            sk.addTime(elapsed)
        }
        let duration = off / sk.timeScale;
        await new Promise(resolve => {
            sk.scheduleOnce(resolve, duration);
        })
        return true
    } catch (error) {
        console.warn(name, error)
        return false
    }
}

//倒放
sp.Skeleton.prototype.playBackAnimation = async function (name: string, timeScale: number = 1) {
    try {
        let sk: sp.Skeleton = this
        let cur = sk.getAnimationDuration(name)
        let track = sk.setAnimation(0, name, false);
        track.trackTime = cur
        await new Promise(r => {
            let cb = (dt: number) => {
                cur -= dt * timeScale
                track.trackTime = cur
                if (cur <= 0) {
                    sk.clearTracks()
                    sk.unschedule(cb)
                    r(null)
                }
            }
            sk.scheduleUpdate(cb)
        })
        return true
    } catch (error) {
        console.warn(name, error)
        return false
    }
}

//为了兼容各种动画，按anims顺序依次找可播放的动画
sp.Skeleton.prototype.tryPlayAnimations = async function (names: string[], loop = false, elapsed: number = 0) {
    let sk: sp.Skeleton = this
    for (let anim of names) {
        if (anim && sk.findAnimation(anim)) {
            return sk.playAnimation(anim, loop, elapsed)
        }
    }
    return false
}

//获取动画时长
sp.Skeleton.prototype.getAnimationDuration = function (name: string) {
    let sk: sp.Skeleton = this
    let anim = sk.findAnimation(name);
    if (!anim) {
        cc.warn("anim not found", name, this.node.getPath());
        return 0;
    }
    return anim.duration;
}

//根据皮肤，获取插槽上了附件
sp.Skeleton.prototype.getSkinAttachment = function (slotName: string, attachmentName: string, skinName: string = "default") {
    if (!this._skeleton) return
    let skin = this.findSkin(skinName)
    let slot = this.findSlot(slotName)
    if (!skin || !slot) return
    let index = slot.data.index
    return skin.getAttachment(index, attachmentName)
}

sp.Skeleton.prototype.findSkin = function (skinName?: string) {
    if (!this._skeleton) return
    let skin
    if (skinName) {
        skin = this._skeleton.data.findSkin(skinName);
        if (CC_DEV && !skin) {
            // console.warn("skin not found", skinName)
        }
    }
    return skin
}

//设置插槽是否显示
sp.Skeleton.prototype.setSlotActive = function (slotName: string, active: boolean) {
    if (!this._skeleton) return
    let slot = this.findSlot(slotName)
    if (!slot) return
    slot.invisible = !active
}

//同一bone下的slot显示切换
sp.Skeleton.prototype.switchSlot = function (slotName: string) {
    if (!this._skeleton) return
    let slot = this.findSlot(slotName)
    if (!slot) return
    for (let st of this._skeleton.slots) {
        if (st.bone == slot.bone) {
            st.invisible = st.data.name != slotName
        }
    }
}

sp.Skeleton.prototype.mix = function (anim1: string, anim2: string, dur: number = 0.15) {
    if (!this.findAnimation(anim1) || !this.findAnimation(anim2)) return
    this.setMix(anim1, anim2, dur)
}

//双向mix
sp.Skeleton.prototype.mix2 = function (anim1: string, anim2: string, dur1: number = 0.15, dur2?: number) {
    if (!this.findAnimation(anim1) || !this.findAnimation(anim2)) return
    this.setMix(anim1, anim2, dur1)
    dur2 = dur2 || dur1
    this.setMix(anim2, anim1, dur2)
}

sp.Skeleton.prototype.addTime = function (time: number) {
    this.update(time);
}

// 获取当前已播放时长
sp.Skeleton.prototype.getAnimationTime = function (trackIndex: number = 0) {
    if (this.isAnimationCached()) {
        return this._accTime;
    }
    else {
        let track = this.getCurrent(trackIndex);
        return track.getAnimationTime();
    }
}

//设置slot上的attachment，用于局部换装需求, 目前仅支持region
sp.Skeleton.prototype.setSlotAttachment = function (slotName: string, spf: cc.Texture2D | cc.SpriteFrame, opt?) {
    const slot = this.findSlot(slotName);
    if (!slot) {
        twlog.error("setSlotAttachment not found", slotName, this.node.getPath())
        return
    }

    let tex2d, rect
    if (spf instanceof cc.SpriteFrame) {
        tex2d = spf.getTexture()
        rect = spf.getRect()
    }
    else {
        tex2d = spf
    }
    let tex2dW = tex2d?.width;
    let tex2dH = tex2d?.height;

    if (CC_JSB) {
        let jsbTex2d
        if (tex2d) {
            // @ts-ignore
            jsbTex2d = new middleware.Texture2D();
            jsbTex2d.setPixelsWide(tex2d.width);
            jsbTex2d.setPixelsHigh(tex2d.height);
            jsbTex2d.setNativeTexture(tex2d.getImpl());
        }
        this._nativeSkeleton.updateRegion(slotName, jsbTex2d)

        let attachment = slot.getAttachment();
        if (attachment) {
            let w = attachment.width, h = attachment.height
            if (rect) {
                w = rect.width, h = rect.height
                attachment.setX(attachment.getX() + (tex2dW - w) * 0.5 - rect.x)
                attachment.setY(attachment.getY() + rect.y - (tex2dH - h) * 0.5)
            }
        
            if (opt?.anchorX !== undefined) {
                attachment.setX(attachment.getX() + w * (0.5 - opt.anchorX))
            }
            if (opt?.anchorY !== undefined) {
                attachment.setY(attachment.getY() + h * (0.5 - opt.anchorY))
            }
            if (opt?.flipY) {
                attachment.setScaleY(-1)
            }
            attachment.updateOffset()
        }
    }
    else {
        if (!spf) {
            slot.attachment = null
            return
        }
        let attachment = slot.getAttachment();
        if (!attachment) {
            attachment = new sp.spine.RegionAttachment(slotName)
            slot.setAttachment(attachment)
        }
    
        // const isMesh = attachment instanceof sp.spine.MeshAttachment;
        const isRegion = attachment instanceof sp.spine.RegionAttachment;
        if (!isRegion) {
            cc.error("setSlotAttachment type not support")
            return
        }

        // image, format, premultiplyAlpha can not be updated in native.  
        // tex2d.setPremultiplyAlpha(true)
    
        let region = attachment.region
        if (!region) {
            region = new sp.spine.TextureAtlasRegion();
        }
        //@ts-ignore
        let skelTex = new sp.SkeletonTexture({ width: tex2dW, height: tex2dH } as ImageBitmap);
        skelTex.setRealTexture(tex2d);
    
        region.width = tex2dW;
        region.height = tex2dH;
        region.originalWidth = tex2dW;
        region.originalHeight = tex2dH;
        region.rotate = false;
        region.u = 0;
        region.v = 0;
        region.u2 = 1;
        region.v2 = 1;
        region.texture = skelTex;
        region.renderObject = region;
        region.offsetX = 0
        region.offsetY = 0
    
        attachment.region = region;
        attachment.width = tex2dW;
        attachment.height = tex2dH;
        attachment.x = 0, attachment.y = 0
    
        let w = attachment.width, h = attachment.height
        if (rect) {
            w = rect.width, h = rect.height
            attachment.x += (tex2dW - w) * 0.5 - rect.x
            attachment.y += rect.y - (tex2dH - h) * 0.5
        }
    
        if (opt?.anchorX !== undefined) {
            attachment.x += w * (0.5 - opt.anchorX)
        }
        if (opt?.anchorY !== undefined) {
            attachment.y += h * (0.5 - opt.anchorY)
        }
        if (opt?.flipY) {
            attachment.scaleY = -1
        }
    
        // if (isMesh) {
        //     attachment.updateUVs();
        // } else if (isRegion) {
            attachment.setRegion(region);
            attachment.updateOffset();
        // }
    }
}

//生成并获取挂点
sp.Skeleton.prototype.getAttachedNode = function (name, syncImmediate: boolean = true) {
    let attachUtil = this.attachUtil;
    let boneNode = attachUtil.getAttachedNodes(name)[0] || attachUtil.generateAttachedNodes(name)[0]
    if (syncImmediate) {
        attachUtil._syncAttachedNode()
    }
    // if (!boneNode) {
    //     console.warn("getAttachedNode not found", this.node.getPath(), name)
    // }
    return boneNode
}

//获取动画的事件
sp.Skeleton.prototype.getEvent = function (animName, eventName = "effect") {
    let anim = this.findAnimation(animName)
    if (!anim) {
        twlog.error("getEvent anim not found", animName, eventName)
        return
    }
    let eventTimeline = anim.timelines.find(t => t instanceof sp.spine.EventTimeline)
    if (!eventTimeline) {
        // twlog.error("getEvent eventTimeline not found", animName, eventName)
        return
    }
    let events = eventTimeline.events
    return events.find(e => e.data?.name == eventName)
}

//获取动画所有的事件
sp.Skeleton.prototype.getEvents = function (animName) {
    let anim = this.findAnimation(animName)
    if (!anim) {
        twlog.error("getEvent anim not found", animName)
        return []
    }
    let eventTimeline = anim.timelines.find(t => t instanceof sp.spine.EventTimeline)
    if (!eventTimeline) {
        twlog.error("getEvent eventTimeline not found", animName)
        return []
    }
    let events = eventTimeline.events
    return events
}

//获取动画的事件 触发时间
sp.Skeleton.prototype.getEventTime = function (animName, eventName = "effect") {
    return this.getEvent(animName, eventName)?.time || 0
}

//jsb 加速用
sp.Skeleton.prototype.setAccTimeScale = function (timeScale) {
    this.accTimeScale = timeScale
    if (this._nativeSkeleton) {
        this._nativeSkeleton.setAccTimeScale(timeScale)
    }
}

sp.Skeleton.prototype.getAnimations = function () {
    let anims = this._skeleton?.data?.animations || []
    return anims.map(a => a.name)
}

const _tmpGetBoundsOffset = new sp.spine.Vector2()
const _tmpGetBoundsSize = new sp.spine.Vector2()
sp.Skeleton.prototype.getBounds = function () {
    const sk: sp.spine.Skeleton = this["_skeleton"]
    const off = _tmpGetBoundsOffset
    const size = _tmpGetBoundsSize
    sk.getBounds(off, size, [])

    const x = off.x
    const y = off.y
    const w = size.x
    const h = size.y
    return cc.rect(x, y, w, h)
}
